import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import HomeScreen from './HomeScreen';
import CreateScreen from './CreateScreen';
import DetailsScreen from './DetailsScreen';

const Stack = createStackNavigator();

export default function HomeStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="HomeMain" 
        component={HomeScreen} 
        options={{ title: 'Accueil' }}
      />
      <Stack.Screen 
        name="Create" 
        component={CreateScreen} 
        options={{ title: 'Créer' }}
      />
    </Stack.Navigator>
  );
}
