import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function ProfileScreen() {
  const handlePress = (action: string) => {
    Alert.alert('Action', `Vous avez sélectionné: ${action}`);
  };

  return (
    <View style={styles.container}>
      {/* Header avec avatar */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Ionicons name="person" size={48} color="#FFFFFF" />
        </View>
        <Text style={styles.name}>Utilisateur</Text>
        <Text style={styles.email}><EMAIL></Text>
      </View>

      {/* Quelques options simples */}
      <View style={styles.options}>
        <Pressable style={styles.option} onPress={() => handlePress('Paramètres')}>
          <Ionicons name="settings" size={24} color="#007AFF" />
          <Text style={styles.optionText}>Paramètres</Text>
          <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
        </Pressable>

        <Pressable style={styles.option} onPress={() => handlePress('Aide')}>
          <Ionicons name="help-circle" size={24} color="#007AFF" />
          <Text style={styles.optionText}>Aide</Text>
          <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
        </Pressable>
      </View>

      {/* Bouton de déconnexion */}
      <Pressable
        style={styles.logoutButton}
        onPress={() => handlePress('Déconnexion')}
      >
        <Ionicons name="log-out" size={20} color="#FF3B30" />
        <Text style={styles.logoutText}>Se déconnecter</Text>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    padding: 16,
  },
  header: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
    borderRadius: 12,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: '#8E8E93',
  },
  options: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 20,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E5EA',
  },
  optionText: {
    fontSize: 16,
    color: '#1C1C1E',
    marginLeft: 12,
    flex: 1,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FF3B30',
    marginTop: 'auto',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF3B30',
    marginLeft: 8,
  },
});
