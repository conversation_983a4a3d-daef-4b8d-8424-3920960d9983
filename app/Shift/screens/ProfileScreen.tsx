import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ProfileItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  showArrow?: boolean;
}

function ProfileItem({ icon, title, subtitle, onPress, showArrow = true }: ProfileItemProps) {
  return (
    <Pressable style={styles.profileItem} onPress={onPress}>
      <View style={styles.profileItemLeft}>
        <View style={styles.profileItemIcon}>
          <Ionicons name={icon as any} size={24} color="#007AFF" />
        </View>
        <View>
          <Text style={styles.profileItemTitle}>{title}</Text>
          {subtitle && <Text style={styles.profileItemSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {showArrow && <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />}
    </Pressable>
  );
}

export default function ProfileScreen() {
  const handlePress = (action: string) => {
    Alert.alert('Action', `Vous avez sélectionné: ${action}`);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header avec avatar */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Ionicons name="person" size={48} color="#FFFFFF" />
        </View>
        <Text style={styles.name}>Utilisateur</Text>
        <Text style={styles.email}><EMAIL></Text>
        <Text style={styles.role}>Développeur</Text>
      </View>

      {/* Statistiques rapides */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>12</Text>
          <Text style={styles.statLabel}>Projets</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>45</Text>
          <Text style={styles.statLabel}>Tâches</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>8</Text>
          <Text style={styles.statLabel}>Équipes</Text>
        </View>
      </View>

      {/* Section Compte */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Compte</Text>
        <View style={styles.sectionContent}>
          <ProfileItem
            icon="person-circle"
            title="Informations personnelles"
            subtitle="Nom, email, téléphone"
            onPress={() => handlePress('Informations personnelles')}
          />
          <ProfileItem
            icon="shield-checkmark"
            title="Sécurité"
            subtitle="Mot de passe, authentification"
            onPress={() => handlePress('Sécurité')}
          />
          <ProfileItem
            icon="notifications"
            title="Notifications"
            subtitle="Gérer vos préférences"
            onPress={() => handlePress('Notifications')}
          />
        </View>
      </View>

      {/* Section Préférences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Préférences</Text>
        <View style={styles.sectionContent}>
          <ProfileItem
            icon="color-palette"
            title="Thème"
            subtitle="Clair, sombre, automatique"
            onPress={() => handlePress('Thème')}
          />
          <ProfileItem
            icon="language"
            title="Langue"
            subtitle="Français"
            onPress={() => handlePress('Langue')}
          />
          <ProfileItem
            icon="settings"
            title="Paramètres avancés"
            onPress={() => handlePress('Paramètres avancés')}
          />
        </View>
      </View>

      {/* Section Support */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Support</Text>
        <View style={styles.sectionContent}>
          <ProfileItem
            icon="help-circle"
            title="Aide et support"
            onPress={() => handlePress('Aide et support')}
          />
          <ProfileItem
            icon="chatbubble"
            title="Nous contacter"
            onPress={() => handlePress('Nous contacter')}
          />
          <ProfileItem
            icon="star"
            title="Évaluer l'application"
            onPress={() => handlePress('Évaluer l\'application')}
          />
        </View>
      </View>

      {/* Bouton de déconnexion */}
      <View style={styles.section}>
        <Pressable 
          style={styles.logoutButton} 
          onPress={() => handlePress('Déconnexion')}
        >
          <Ionicons name="log-out" size={20} color="#FF3B30" />
          <Text style={styles.logoutText}>Se déconnecter</Text>
        </Pressable>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>Version 1.0.0</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 4,
  },
  role: {
    fontSize: 14,
    color: '#007AFF',
    backgroundColor: '#007AFF20',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 20,
    borderRadius: 12,
    padding: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#8E8E93',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginLeft: 16,
    marginBottom: 8,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    borderRadius: 12,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E5EA',
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileItemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  profileItemTitle: {
    fontSize: 16,
    fontWeight: '400',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  profileItemSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF3B30',
    marginLeft: 8,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#C7C7CC',
  },
});
