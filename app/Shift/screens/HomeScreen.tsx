import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Pressable,
  Alert 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

interface ContentItem {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  color: string;
}

const contentItems: ContentItem[] = [
  {
    id: '1',
    title: 'Projet Alpha',
    subtitle: 'Application mobile',
    description: 'Développement d\'une application mobile innovante avec React Native.',
    icon: 'phone-portrait',
    color: '#FF6B6B',
  },
  {
    id: '2',
    title: 'Site Web Beta',
    subtitle: 'Interface utilisateur',
    description: 'Création d\'une interface web moderne et responsive.',
    icon: 'globe',
    color: '#4ECDC4',
  },
  {
    id: '3',
    title: 'API Gamma',
    subtitle: 'Backend service',
    description: 'Développement d\'une API REST robuste et sécurisée.',
    icon: 'server',
    color: '#45B7D1',
  },
];

interface ContentCardProps {
  item: ContentItem;
  onPress: () => void;
}

function ContentCard({ item, onPress }: ContentCardProps) {
  return (
    <Pressable style={[styles.card, { borderLeftColor: item.color }]} onPress={onPress}>
      <View style={styles.cardHeader}>
        <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
          <Ionicons name={item.icon as any} size={24} color={item.color} />
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.cardTitle}>{item.title}</Text>
          <Text style={styles.cardSubtitle}>{item.subtitle}</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
      </View>
      <Text style={styles.cardDescription}>{item.description}</Text>
    </Pressable>
  );
}

export default function HomeScreen() {
  const navigation = useNavigation();

  const handleCreatePress = () => {
    navigation.navigate('Create' as never);
  };

  const handleItemPress = (item: ContentItem) => {
    navigation.navigate('Details' as never, { item } as never);
  };

  const handleQuickAction = (action: string) => {
    Alert.alert('Action rapide', `Vous avez sélectionné: ${action}`);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Bienvenue sur Shift</Text>
        <Text style={styles.subtitle}>Gérez vos projets facilement</Text>
      </View>

      {/* Bouton de création principal */}
      <Pressable style={styles.createButton} onPress={handleCreatePress}>
        <Ionicons name="add-circle" size={24} color="#FFFFFF" />
        <Text style={styles.createButtonText}>Créer un nouveau projet</Text>
      </Pressable>

      {/* Actions rapides */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions rapides</Text>
        <View style={styles.quickActions}>
          <Pressable 
            style={styles.quickAction} 
            onPress={() => handleQuickAction('Template')}
          >
            <Ionicons name="document-text" size={20} color="#007AFF" />
            <Text style={styles.quickActionText}>Template</Text>
          </Pressable>
          <Pressable 
            style={styles.quickAction} 
            onPress={() => handleQuickAction('Import')}
          >
            <Ionicons name="cloud-download" size={20} color="#007AFF" />
            <Text style={styles.quickActionText}>Importer</Text>
          </Pressable>
          <Pressable 
            style={styles.quickAction} 
            onPress={() => handleQuickAction('Scan')}
          >
            <Ionicons name="scan" size={20} color="#007AFF" />
            <Text style={styles.quickActionText}>Scanner</Text>
          </Pressable>
        </View>
      </View>

      {/* Liste des projets */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Projets récents</Text>
        {contentItems.map((item) => (
          <ContentCard
            key={item.id}
            item={item}
            onPress={() => handleItemPress(item)}
          />
        ))}
      </View>

      <View style={styles.footer} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    padding: 20,
    paddingTop: 10,
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    marginHorizontal: 16,
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 16,
  },
  quickAction: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    minWidth: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  quickActionText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 4,
    marginHorizontal: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  cardDescription: {
    fontSize: 14,
    color: '#3C3C43',
    lineHeight: 20,
  },
  footer: {
    height: 20,
  },
});
