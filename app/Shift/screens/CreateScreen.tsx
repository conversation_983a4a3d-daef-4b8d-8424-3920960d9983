import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Pressable,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

interface ProjectType {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
}

const projectTypes: ProjectType[] = [
  {
    id: 'mobile',
    title: 'Application Mobile',
    description: 'Créer une app iOS/Android',
    icon: 'phone-portrait',
    color: '#FF6B6B',
  },
  {
    id: 'web',
    title: 'Site Web',
    description: 'Développer un site web',
    icon: 'globe',
    color: '#4ECDC4',
  },
  {
    id: 'api',
    title: 'API Backend',
    description: 'Créer une API REST',
    icon: 'server',
    color: '#45B7D1',
  },
  {
    id: 'desktop',
    title: 'Application Desktop',
    description: 'App pour Windows/Mac/Linux',
    icon: 'desktop',
    color: '#9B59B6',
  },
];

interface ProjectTypeCardProps {
  type: ProjectType;
  isSelected: boolean;
  onPress: () => void;
}

function ProjectTypeCard({ type, isSelected, onPress }: ProjectTypeCardProps) {
  return (
    <Pressable
      style={[
        styles.typeCard,
        { borderColor: type.color },
        isSelected && { backgroundColor: type.color + '20', borderWidth: 2 }
      ]}
      onPress={onPress}
    >
      <View style={[styles.typeIcon, { backgroundColor: type.color + '20' }]}>
        <Ionicons name={type.icon as any} size={24} color={type.color} />
      </View>
      <Text style={styles.typeTitle}>{type.title}</Text>
      <Text style={styles.typeDescription}>{type.description}</Text>
      {isSelected && (
        <View style={styles.selectedIndicator}>
          <Ionicons name="checkmark-circle" size={20} color={type.color} />
        </View>
      )}
    </Pressable>
  );
}

export default function CreateScreen() {
  const navigation = useNavigation();
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [selectedType, setSelectedType] = useState<string | null>(null);

  const handleCreate = () => {
    if (!projectName.trim()) {
      Alert.alert('Erreur', 'Veuillez saisir un nom de projet');
      return;
    }
    
    if (!selectedType) {
      Alert.alert('Erreur', 'Veuillez sélectionner un type de projet');
      return;
    }

    const selectedProjectType = projectTypes.find(type => type.id === selectedType);
    
    Alert.alert(
      'Projet créé !',
      `Projet "${projectName}" de type "${selectedProjectType?.title}" créé avec succès.`,
      [
        {
          text: 'OK',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.form}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations du projet</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nom du projet *</Text>
            <TextInput
              style={styles.input}
              value={projectName}
              onChangeText={setProjectName}
              placeholder="Ex: Mon Super Projet"
              placeholderTextColor="#8E8E93"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={projectDescription}
              onChangeText={setProjectDescription}
              placeholder="Décrivez votre projet..."
              placeholderTextColor="#8E8E93"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Type de projet *</Text>
          <View style={styles.typeGrid}>
            {projectTypes.map((type) => (
              <ProjectTypeCard
                key={type.id}
                type={type}
                isSelected={selectedType === type.id}
                onPress={() => setSelectedType(type.id)}
              />
            ))}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Pressable style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>Annuler</Text>
          </Pressable>
          
          <Pressable style={styles.createButton} onPress={handleCreate}>
            <Ionicons name="add-circle" size={20} color="#FFFFFF" />
            <Text style={styles.createButtonText}>Créer le projet</Text>
          </Pressable>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  form: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1C1C1E',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  textArea: {
    height: 100,
    paddingTop: 12,
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  typeCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    position: 'relative',
  },
  typeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  typeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E8E93',
  },
  createButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});
