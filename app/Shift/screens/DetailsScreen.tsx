import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';

interface ContentItem {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  color: string;
}

interface ActionButtonProps {
  icon: string;
  title: string;
  color: string;
  onPress: () => void;
}

function ActionButton({ icon, title, color, onPress }: ActionButtonProps) {
  return (
    <Pressable style={styles.actionButton} onPress={onPress}>
      <View style={[styles.actionIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon as any} size={20} color={color} />
      </View>
      <Text style={styles.actionTitle}>{title}</Text>
    </Pressable>
  );
}

interface InfoSectionProps {
  title: string;
  items: string[];
  icon: string;
  color: string;
}

function InfoSection({ title, items, icon, color }: InfoSectionProps) {
  return (
    <View style={styles.infoSection}>
      <View style={styles.infoHeader}>
        <View style={[styles.infoIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon as any} size={20} color={color} />
        </View>
        <Text style={styles.infoTitle}>{title}</Text>
      </View>
      {items.map((item, index) => (
        <View key={index} style={styles.infoItem}>
          <Ionicons name="checkmark-circle" size={16} color={color} />
          <Text style={styles.infoText}>{item}</Text>
        </View>
      ))}
    </View>
  );
}

export default function DetailsScreen() {
  const route = useRoute();
  const navigation = useNavigation();
  const { item } = route.params as { item: ContentItem };

  const handleAction = (action: string) => {
    Alert.alert('Action', `Action "${action}" sélectionnée pour ${item.title}`);
  };

  const handleEdit = () => {
    Alert.alert('Modifier', `Modification de ${item.title}`);
  };

  const handleDelete = () => {
    Alert.alert(
      'Supprimer',
      `Êtes-vous sûr de vouloir supprimer ${item.title} ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Supprimer', 
          style: 'destructive',
          onPress: () => {
            Alert.alert('Supprimé', `${item.title} a été supprimé`);
            navigation.goBack();
          }
        },
      ]
    );
  };

  // Données d'exemple pour les sections
  const features = [
    'Interface utilisateur moderne',
    'Performance optimisée',
    'Sécurité renforcée',
    'Support multiplateforme',
  ];

  const technologies = [
    'React Native',
    'TypeScript',
    'Expo',
    'React Navigation',
  ];

  const timeline = [
    'Phase 1: Conception et design',
    'Phase 2: Développement core',
    'Phase 3: Tests et optimisation',
    'Phase 4: Déploiement',
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.headerIcon, { backgroundColor: item.color + '20' }]}>
          <Ionicons name={item.icon as any} size={48} color={item.color} />
        </View>
        <Text style={styles.title}>{item.title}</Text>
        <Text style={styles.subtitle}>{item.subtitle}</Text>
        <Text style={styles.description}>{item.description}</Text>
      </View>

      {/* Actions rapides */}
      <View style={styles.actionsContainer}>
        <ActionButton
          icon="play"
          title="Démarrer"
          color="#34C759"
          onPress={() => handleAction('Démarrer')}
        />
        <ActionButton
          icon="pause"
          title="Pause"
          color="#FF9500"
          onPress={() => handleAction('Pause')}
        />
        <ActionButton
          icon="share"
          title="Partager"
          color="#007AFF"
          onPress={() => handleAction('Partager')}
        />
        <ActionButton
          icon="settings"
          title="Config"
          color="#8E8E93"
          onPress={() => handleAction('Configuration')}
        />
      </View>

      {/* Sections d'informations */}
      <InfoSection
        title="Fonctionnalités"
        items={features}
        icon="star"
        color="#FF6B6B"
      />

      <InfoSection
        title="Technologies"
        items={technologies}
        icon="code-slash"
        color="#4ECDC4"
      />

      <InfoSection
        title="Planning"
        items={timeline}
        icon="calendar"
        color="#45B7D1"
      />

      {/* Statistiques */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>Statistiques</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>85%</Text>
            <Text style={styles.statLabel}>Progression</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Tâches</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>3</Text>
            <Text style={styles.statLabel}>Membres</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>7j</Text>
            <Text style={styles.statLabel}>Restant</Text>
          </View>
        </View>
      </View>

      {/* Boutons d'action */}
      <View style={styles.bottomActions}>
        <Pressable style={styles.editButton} onPress={handleEdit}>
          <Ionicons name="create" size={20} color="#007AFF" />
          <Text style={styles.editButtonText}>Modifier</Text>
        </Pressable>
        
        <Pressable style={styles.deleteButton} onPress={handleDelete}>
          <Ionicons name="trash" size={20} color="#FF3B30" />
          <Text style={styles.deleteButtonText}>Supprimer</Text>
        </Pressable>
      </View>

      <View style={styles.footer} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  headerIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1C1E',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#3C3C43',
    textAlign: 'center',
    lineHeight: 22,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
  },
  actionButton: {
    alignItems: 'center',
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 12,
    color: '#1C1C1E',
    textAlign: 'center',
  },
  infoSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 20,
    borderRadius: 12,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 16,
    color: '#3C3C43',
    marginLeft: 12,
    flex: 1,
  },
  statsContainer: {
    marginHorizontal: 16,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'center',
  },
  bottomActions: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 20,
    gap: 12,
  },
  editButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#007AFF',
    marginLeft: 8,
  },
  deleteButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  deleteButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF3B30',
    marginLeft: 8,
  },
  footer: {
    height: 20,
  },
});
