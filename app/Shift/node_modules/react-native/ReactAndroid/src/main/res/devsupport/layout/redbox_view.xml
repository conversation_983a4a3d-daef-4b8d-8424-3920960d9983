<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#1A1A1A"
    >
    <ListView
        android:id="@+id/rn_redbox_stack"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:divider="@null"
        android:dividerHeight="0dp"
        />
    <View
        android:id="@+id/rn_redbox_line_separator"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@android:color/darker_gray"
        android:visibility="gone"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <ProgressBar
            android:id="@+id/rn_redbox_loading_indicator"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            style="@android:style/Widget.ProgressBar.Small"
            android:indeterminateOnly="true"
            android:visibility="gone"
            android:paddingLeft="16dp"
            />
        <TextView
            android:id="@+id/rn_redbox_report_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:visibility="gone"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:lineSpacingExtra="4dp"
            />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/redbox_top_border_background"
        >
        <Button
            android:id="@+id/rn_redbox_dismiss_button"
            android:text="@string/catalyst_dismiss_button"
            style="@style/redboxButton"
            />
        <Button
            android:id="@+id/rn_redbox_reload_button"
            android:text="@string/catalyst_reload_button"
            style="@style/redboxButton"
            />
        <Button
            android:id="@+id/rn_redbox_report_button"
            android:text="@string/catalyst_report_button"
            android:visibility="gone"
            style="@style/redboxButton"
            />
    </LinearLayout>
</LinearLayout>
