/**
 * @flow strict
 */

import type { Icon } from './index';

export type EvilIconsGlyphs = 'archive' | 'arrow-down' | 'arrow-left' | 'arrow-right' | 'arrow-up' | 'bell' | 'calendar' | 'camera' | 'cart' | 'chart' | 'check' | 'chevron-down' | 'chevron-left' | 'chevron-right' | 'chevron-up' | 'clock' | 'close' | 'close-o' | 'comment' | 'credit-card' | 'envelope' | 'exclamation' | 'external-link' | 'eye' | 'gear' | 'heart' | 'image' | 'like' | 'link' | 'location' | 'lock' | 'minus' | 'navicon' | 'paperclip' | 'pencil' | 'play' | 'plus' | 'pointer' | 'question' | 'redo' | 'refresh' | 'retweet' | 'sc-facebook' | 'sc-github' | 'sc-google-plus' | 'sc-instagram' | 'sc-linkedin' | 'sc-odnoklassniki' | 'sc-pinterest' | 'sc-skype' | 'sc-soundcloud' | 'sc-telegram' | 'sc-tumblr' | 'sc-twitter' | 'sc-vimeo' | 'sc-vk' | 'sc-youtube' | 'search' | 'share-apple' | 'share-google' | 'spinner' | 'spinner-2' | 'spinner-3' | 'star' | 'tag' | 'trash' | 'trophy' | 'undo' | 'unlock' | 'user';

declare export default Class<Icon<EvilIconsGlyphs>>;
