/**
 * Register font asset source folder
 */
android.sourceSets.main.assets.srcDirs += file("$buildDir/intermediates/ReactNativeVectorIcons")

/**
 * Task to copy icon font files
 */
afterEvaluate {
    def config = project.hasProperty("vectoricons") ? project.vectoricons : [];
    def iconFontsDir = config.iconFontsDir ?: "../../node_modules/react-native-vector-icons/Fonts";
    def iconFontNames = config.iconFontNames ?: [ "*.ttf" ];

    def fontCopyTask = tasks.create(
        name: "copyReactNativeVectorIconFonts",
        type: Copy) {
        description = "copy vector icon fonts."
        into "$buildDir/intermediates/ReactNativeVectorIcons/fonts"

        iconFontNames.each { fontName ->
            from(iconFontsDir) {
                include(fontName)
            }
        }
    }

    android.applicationVariants.all { variant ->
        def variantName = variant.name.capitalize()
        def taskNames = [
                "generate${variantName}Assets",
                "generate${variantName}LintModel",
                "generate${variantName}LintReportModel",
                "generate${variantName}LintVitalReportModel",
                "lintAnalyze${variantName}",
                "lintVitalAnalyze${variantName}",
        ]

        taskNames.each { taskName ->
            def task = tasks.findByName(taskName)
            if (task) {
                task.dependsOn(fontCopyTask)
            }
        }
    }
}
