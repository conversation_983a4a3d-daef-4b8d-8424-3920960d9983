{"version": 3, "names": ["getInvertedMultiplier", "getDistanceForDirection", "layout", "gestureDirection", "isRTL", "multiplier", "height", "width"], "sourceRoot": "../../../src", "sources": ["utils/getDistanceForDirection.tsx"], "mappings": ";;AACA,SAASA,qBAAqB,QAAQ,4BAAyB;AAE/D,OAAO,SAASC,uBAAuBA,CACrCC,MAAc,EACdC,gBAAkC,EAClCC,KAAc,EACN;EACR,MAAMC,UAAU,GAAGL,qBAAqB,CAACG,gBAAgB,EAAEC,KAAK,CAAC;EAEjE,QAAQD,gBAAgB;IACtB,KAAK,UAAU;IACf,KAAK,mBAAmB;MACtB,OAAOD,MAAM,CAACI,MAAM,GAAGD,UAAU;IACnC,KAAK,YAAY;IACjB,KAAK,qBAAqB;MACxB,OAAOH,MAAM,CAACK,KAAK,GAAGF,UAAU;EACpC;AACF", "ignoreList": []}