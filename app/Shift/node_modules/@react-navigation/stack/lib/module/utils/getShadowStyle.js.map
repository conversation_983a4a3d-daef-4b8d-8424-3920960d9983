{"version": 3, "names": ["Color", "Platform", "getShadowStyle", "offset", "radius", "opacity", "color", "result", "select", "web", "boxShadow", "width", "height", "alpha", "toString", "default", "shadowOffset", "shadowRadius", "shadowColor", "shadowOpacity"], "sourceRoot": "../../../src", "sources": ["utils/getShadowStyle.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AAYvC,OAAO,SAASC,cAAcA,CAAC;EAC7BC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,KAAK,GAAG;AACI,CAAC,EAAE;EACf,MAAMC,MAAM,GAAGN,QAAQ,CAACO,MAAM,CAAC;IAC7BC,GAAG,EAAE;MACHC,SAAS,EAAE,GAAGP,MAAM,CAACQ,KAAK,MAAMR,MAAM,CAACS,MAAM,MAAMR,MAAM,MAAMJ,KAAK,CAClEM,KACF,CAAC,CACEO,KAAK,CAACR,OAAO,CAAC,CACdS,QAAQ,CAAC,CAAC;IACf,CAAC;IACDC,OAAO,EAAE;MACPC,YAAY,EAAEb,MAAM;MACpBc,YAAY,EAAEb,MAAM;MACpBc,WAAW,EAAEZ,KAAK;MAClBa,aAAa,EAAEd;IACjB;EACF,CAAC,CAAC;EAEF,OAAOE,MAAM;AACf", "ignoreList": []}