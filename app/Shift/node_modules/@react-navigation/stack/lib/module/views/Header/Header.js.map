{"version": 3, "names": ["getHeaderTitle", "HeaderShownContext", "StackActions", "React", "useSafeAreaInsets", "ModalPresentationContext", "throttle", "HeaderSegment", "jsx", "_jsx", "Header", "memo", "back", "layout", "progress", "options", "route", "navigation", "styleInterpolator", "insets", "previousTitle", "headerBackTitle", "undefined", "title", "goBack", "useCallback", "isFocused", "canGoBack", "dispatch", "pop", "source", "key", "isModal", "useContext", "isParentHeaderShown", "statusBarHeight", "headerStatusBarHeight", "top", "name", "modal", "onGoBack", "backHref", "href"], "sourceRoot": "../../../../src", "sources": ["views/Header/Header.tsx"], "mappings": ";;AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,4BAA4B;AAC/E,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAGlE,SAASC,wBAAwB,QAAQ,yCAAsC;AAC/E,SAASC,QAAQ,QAAQ,yBAAsB;AAC/C,SAASC,aAAa,QAAQ,oBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEhD,OAAO,MAAMC,MAAM,gBAAGP,KAAK,CAACQ,IAAI,CAAC,SAASD,MAAMA,CAAC;EAC/CE,IAAI;EACJC,MAAM;EACNC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACLC,UAAU;EACVC;AACgB,CAAC,EAAE;EACnB,MAAMC,MAAM,GAAGf,iBAAiB,CAAC,CAAC;EAElC,IAAIgB,aAAa;;EAEjB;EACA;EACA,IAAIL,OAAO,CAACM,eAAe,KAAKC,SAAS,EAAE;IACzCF,aAAa,GAAGL,OAAO,CAACM,eAAe;EACzC,CAAC,MAAM,IAAIT,IAAI,EAAE;IACfQ,aAAa,GAAGR,IAAI,CAACW,KAAK;EAC5B;;EAEA;EACA,MAAMC,MAAM,GAAGrB,KAAK,CAACsB,WAAW,CAC9BnB,QAAQ,CAAC,MAAM;IACb,IAAIW,UAAU,CAACS,SAAS,CAAC,CAAC,IAAIT,UAAU,CAACU,SAAS,CAAC,CAAC,EAAE;MACpDV,UAAU,CAACW,QAAQ,CAAC;QAClB,GAAG1B,YAAY,CAAC2B,GAAG,CAAC,CAAC;QACrBC,MAAM,EAAEd,KAAK,CAACe;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC,EACN,CAACd,UAAU,EAAED,KAAK,CAACe,GAAG,CACxB,CAAC;EAED,MAAMC,OAAO,GAAG7B,KAAK,CAAC8B,UAAU,CAAC5B,wBAAwB,CAAC;EAC1D,MAAM6B,mBAAmB,GAAG/B,KAAK,CAAC8B,UAAU,CAAChC,kBAAkB,CAAC;EAEhE,MAAMkC,eAAe,GACnBpB,OAAO,CAACqB,qBAAqB,KAAKd,SAAS,GACvCP,OAAO,CAACqB,qBAAqB,GAC7BJ,OAAO,IAAIE,mBAAmB,GAC5B,CAAC,GACDf,MAAM,CAACkB,GAAG;EAElB,oBACE5B,IAAA,CAACF,aAAa;IAAA,GACRQ,OAAO;IACXQ,KAAK,EAAEvB,cAAc,CAACe,OAAO,EAAEC,KAAK,CAACsB,IAAI,CAAE;IAC3CxB,QAAQ,EAAEA,QAAS;IACnBD,MAAM,EAAEA,MAAO;IACf0B,KAAK,EAAEP,OAAQ;IACfX,eAAe,EACbN,OAAO,CAACM,eAAe,KAAKC,SAAS,GACjCP,OAAO,CAACM,eAAe,GACvBD,aACL;IACDgB,qBAAqB,EAAED,eAAgB;IACvCK,QAAQ,EAAE5B,IAAI,GAAGY,MAAM,GAAGF,SAAU;IACpCmB,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC8B,IAAI,GAAGpB,SAAU;IACvCJ,iBAAiB,EAAEA;EAAkB,CACtC,CAAC;AAEN,CAAC,CAAC", "ignoreList": []}